import { useState } from 'react';
import animation11 from '../../assets/photos/animation11.png';

const VideoSection = () => {
  const [playingVideo, setPlayingVideo] = useState(null);
  const [hoveredVideo, setHoveredVideo] = useState(null);

  // Video gallery data
  const videos = [
    {
      id: 1,
      youtubeId: 'rvmEz9RiDZ8',
      title: 'Discover North East India',
      description: 'Experience the magical landscapes and rich culture',
      thumbnail: animation11,
      duration: '3:45'
    },
    {
      id: 2,
      youtubeId: 'dQw4w9WgXcQ', // Replace with actual video IDs
      title: 'Sikkim Adventures',
      description: 'Journey through the pristine mountains of Sikkim',
      thumbnail: animation11,
      duration: '5:20'
    },
    {
      id: 3,
      youtubeId: 'jNQXAC9IVRw', // Replace with actual video IDs
      title: 'Assam Tea Gardens',
      description: 'Explore the lush tea gardens and wildlife',
      thumbnail: animation11,
      duration: '4:15'
    },
    {
      id: 4,
      youtubeId: 'M7lc1UVf-VE', // Replace with actual video IDs
      title: 'Meghalaya Waterfalls',
      description: 'Witness the breathtaking waterfalls and caves',
      thumbnail: animation11,
      duration: '6:30'
    }
  ];

  const handlePlayVideo = (videoId) => {
    setPlayingVideo(videoId);
  };

  const handleCloseVideo = () => {
    setPlayingVideo(null);
  };

  return (
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-light mb-4 font-serif">
            Discover the{" "}
            <span className='text-blue-400'>Magic</span> of North East India
          </h2>
          <div className="w-[1px] h-16 bg-gray-200 mx-auto mb-4"></div>
          <p className="max-w-xl mx-auto text-gray-600">
            Experience the beauty of North East India through our curated video collection
          </p>
        </div>

        {/* Modern Video Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 max-w-6xl mx-auto">
          {videos.map((video, index) => (
            <div
              key={video.id}
              className="group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2"
              onMouseEnter={() => setHoveredVideo(video.id)}
              onMouseLeave={() => setHoveredVideo(null)}
            >
              {/* Video Thumbnail */}
              <div className="relative h-64 overflow-hidden">
                <img
                  src={video.thumbnail}
                  alt={video.title}
                  className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
                />

                {/* Gradient Overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                {/* Duration Badge */}
                <div className="absolute top-4 right-4 bg-black/80 text-white px-3 py-1 rounded-full text-sm font-medium backdrop-blur-sm">
                  {video.duration}
                </div>

                {/* Play Button Overlay */}
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-300">
                  <button
                    onClick={() => handlePlayVideo(video.id)}
                    className="w-20 h-20 bg-white/95 hover:bg-white rounded-full flex items-center justify-center transition-all duration-300 hover:scale-110 shadow-2xl"
                  >
                    <svg
                      width="28"
                      height="28"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                      className="ml-1"
                    >
                      <path d="M8 5v14l11-7-11-7z" fill="#1f2937" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* Video Info */}
              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors duration-300">
                  {video.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-6">
                  {video.description}
                </p>

                {/* Action Button */}
                <button
                  onClick={() => handlePlayVideo(video.id)}
                  className="w-full py-3 bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white font-semibold rounded-xl transition-all duration-300 flex items-center justify-center group/btn shadow-lg hover:shadow-xl"
                >
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                    className="mr-2 transition-transform duration-300 group-hover/btn:scale-110"
                  >
                    <path d="M8 5v14l11-7-11-7z" fill="currentColor" />
                  </svg>
                  Watch Video
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Video Modal with Industry Standard YouTube Controls */}
      {playingVideo && (
        <div className="fixed inset-0 z-50 bg-black/90 backdrop-blur-sm flex items-center justify-center p-4">
          <div className="relative w-full max-w-6xl mx-auto">
            {/* Close Button */}
            <button
              onClick={handleCloseVideo}
              className="absolute -top-12 right-0 text-white hover:text-gray-300 transition-colors z-10"
            >
              <svg
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
              </svg>
            </button>

            {/* Video Container */}
            <div className="relative w-full bg-black rounded-lg overflow-hidden shadow-2xl" style={{ paddingTop: "56.25%" }}>
              <iframe
                className="absolute inset-0 w-full h-full"
                src={`https://www.youtube.com/embed/${videos.find(v => v.id === playingVideo)?.youtubeId}?autoplay=1&rel=0&modestbranding=0&playsinline=1&controls=1&cc_load_policy=1&iv_load_policy=3&enablejsapi=1&origin=${window.location.origin}`}
                title="YouTube video player"
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                allowFullScreen
                style={{ border: 'none' }}
              ></iframe>
            </div>

            {/* Video Info Below */}
            <div className="mt-4 text-center">
              <h3 className="text-2xl font-semibold text-white mb-2">
                {videos.find(v => v.id === playingVideo)?.title}
              </h3>
              <p className="text-gray-300">
                {videos.find(v => v.id === playingVideo)?.description}
              </p>
            </div>
          </div>
        </div>
      )}
    </section>
  );
};

export default VideoSection;
